import pytest
import logging
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch, call
import uuid
from datetime import datetime, timezone
import time

from backend.services.enhanced_policy_generator import (
    EnhancedPolicyGenerator,
    PolicyRequest,
    GenerationStage,
    StageOutput,
    PolicyDocument,
    AIService,
    PolicyPromptManager,
    PolicyQualityValidator
)
from motor.motor_asyncio import AsyncIOMotorDatabase, AsyncIOMotorCollection

@pytest.fixture
def mock_policy_request():
    return PolicyRequest(
        user_id="test_user_123",
        company_name="TestCorp",
        company_type="Tech Startup",
        industry="SaaS",
        employee_count=50,
        framework="ISO 27001",
        data_types=["PII", "Financial Records"],
        existing_policies=["Password Policy", "Data Backup Policy"],
        complexity_level="High",
        jurisdiction="Global", # Example, can also use default "UK"
        risk_profile="High" # Example, can also use default "medium"
    )

@pytest.fixture
def mock_db():
    return MagicMock(spec=AsyncIOMotorDatabase)

@pytest.fixture
def mock_ai_service_instance():
    service = MagicMock(spec=AIService)
    service.generate_content = AsyncMock(return_value="Mocked AI Content")
    # Mock the model attribute if it's accessed (e.g., in metadata)
    service.model = MagicMock()
    service.model.model_name = "mocked-gemini-model"
    return service

@pytest.fixture
@patch('backend.services.enhanced_policy_generator.PolicyPromptManager', spec=PolicyPromptManager)
@patch('backend.services.enhanced_policy_generator.PolicyQualityValidator', spec=PolicyQualityValidator)
# Patch the AIService that is instantiated inside EnhancedPolicyGenerator
@patch('backend.services.enhanced_policy_generator.AIService', spec=AIService)
def enhanced_policy_generator_fixture(MockAIService, MockPolicyQualityValidator, MockPolicyPromptManager, mock_db):
    # Configure the class mocks (the ones that are instantiated inside EPG)
    mock_prompt_manager_instance = MockPolicyPromptManager.return_value
    mock_prompt_manager_instance.get_framework_prompt = MagicMock(return_value="Framework Prompt")
    mock_prompt_manager_instance.get_procedures_prompt = MagicMock(return_value="Procedures Prompt")
    mock_prompt_manager_instance.get_tools_prompt = MagicMock(return_value="Tools Prompt")
    mock_prompt_manager_instance.get_assembly_prompt = MagicMock(return_value="Assembly Prompt")

    mock_quality_validator_instance = MockPolicyQualityValidator.return_value
    mock_quality_validator_instance.validate_framework = AsyncMock(return_value=0.9)
    mock_quality_validator_instance.validate_procedures = AsyncMock(return_value=0.85)
    mock_quality_validator_instance.validate_tools = AsyncMock(return_value=0.88)
    mock_quality_validator_instance.validate_assembly = AsyncMock(return_value=0.95)
    mock_quality_validator_instance.calculate_overall_score = AsyncMock(return_value=0.87)

    mock_ai_service_instance = MockAIService.return_value
    mock_ai_service_instance.generate_content.return_value = "Mocked AI Content"

    generator = EnhancedPolicyGenerator(db=mock_db, ai_service=mock_ai_service_instance)
    
    # The patch decorators replace the classes, so when EPG instantiates them,
    # it gets the mocks. We can then access these mocked instances via the generator.
    generator.prompt_manager = mock_prompt_manager_instance
    generator.quality_validator = mock_quality_validator_instance
    
    # Mock internal helper methods that interact with DB or have complex side effects not being tested directly
    generator._log_stage_completion = AsyncMock()
    
    # Configure mock_db collections' methods for attribute-style access
    # mock_db is the MagicMock(spec=AsyncIOMotorDatabase) instance passed into the EPG
    mock_db.generation_errors = MagicMock(spec=AsyncIOMotorCollection)
    mock_db.generation_errors.insert_one = AsyncMock(return_value=MagicMock(inserted_id="mock_gen_error_id"))
    
    mock_db.policies = MagicMock(spec=AsyncIOMotorCollection)
    mock_db.policies.insert_one = AsyncMock(return_value=MagicMock(inserted_id="mock_policy_doc_id"))

    mock_db.stage_completions = MagicMock(spec=AsyncIOMotorCollection)
    mock_db.stage_completions.insert_one = AsyncMock(return_value=MagicMock(inserted_id="mock_stage_log_id"))

    return generator, mock_ai_service_instance, mock_db, mock_prompt_manager_instance, mock_quality_validator_instance

@pytest.mark.asyncio
async def test_generate_comprehensive_policy_happy_path(enhanced_policy_generator_fixture, mock_policy_request, mocker):
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture

    # Setup return values for each AI generation stage
    data_content = "Collected Data"
    risk_content = "Assessed Risks"
    framework_content = "Generated Framework Content"
    procedures_content = "Generated Procedures Content"
    tools_content = "Generated Tools Content"
    assembled_content = "Assembled Document Content"

    # Since _collect_data and _assess_risks are mocked, AI service is only called for:
    # framework, procedures, tools, assembly
    mock_ai_service.generate_content.side_effect = [
        framework_content, procedures_content, tools_content, assembled_content
    ]
    
    # Mock the internal _collect_data and _assess_risks methods to return predictable StageOutput
    mock_data_output = StageOutput(content=data_content, word_count=2, completeness_score=0.9, generation_time=1.0, metadata={})
    mock_risk_output = StageOutput(content=risk_content, word_count=2, completeness_score=0.8, generation_time=1.0, metadata={})
    mocker.patch.object(generator, '_collect_data', return_value=mock_data_output)
    mocker.patch.object(generator, '_assess_risks', return_value=mock_risk_output)

    mock_save_policy = mocker.patch.object(generator, '_save_enhanced_policy', new_callable=AsyncMock)

    policy_document = await generator.generate_comprehensive_policy(mock_policy_request)

    assert policy_document is not None
    assert isinstance(policy_document, PolicyDocument)
    assert policy_document.policy_id is not None
    assert policy_document.metadata['company_name'] == mock_policy_request.company_name
    assert policy_document.quality_metrics['overall_score'] == 0.87

    # Check that context is passed correctly (matching actual implementation call patterns)
    mock_pm.get_framework_prompt.assert_called_once_with(mock_policy_request, mock_data_output, mock_risk_output)

    # get_procedures_prompt uses positional arguments: (framework_output, request, data_context, risk_context)
    mock_pm.get_procedures_prompt.assert_called_once()
    call_args = mock_pm.get_procedures_prompt.call_args[0]  # positional args
    assert call_args[2] == mock_data_output  # data_context is 3rd positional arg
    assert call_args[3] == mock_risk_output  # risk_context is 4th positional arg

    # get_tools_prompt uses positional arguments: (framework_output, procedures_output, request, data_context, risk_context)
    mock_pm.get_tools_prompt.assert_called_once()
    call_args = mock_pm.get_tools_prompt.call_args[0]  # positional args
    assert call_args[3] == mock_data_output  # data_context is 4th positional arg
    assert call_args[4] == mock_risk_output  # risk_context is 5th positional arg

    # get_assembly_prompt uses keyword arguments for data_context and risk_context
    mock_pm.get_assembly_prompt.assert_called_once()
    assert mock_pm.get_assembly_prompt.call_args[1]['data_context'] == mock_data_output
    assert mock_pm.get_assembly_prompt.call_args[1]['risk_context'] == mock_risk_output

    # Check validator calls
    mock_qv.validate_framework.assert_called_once_with(framework_content)
    mock_qv.validate_procedures.assert_called_once_with(procedures_content)
    mock_qv.validate_tools.assert_called_once_with(tools_content)
    mock_qv.validate_assembly.assert_called_once_with(assembled_content)
    assert mock_qv.calculate_overall_score.call_count == 1

    mock_save_policy.assert_called_once()
    # Check that all stages were logged (only 4 since _collect_data and _assess_risks are mocked)
    assert generator._log_stage_completion.call_count == 4 # Framework, Procedures, Tools, Assembly

@pytest.mark.asyncio
async def test_gcp_framework_fails_returns_none(enhanced_policy_generator_fixture, mock_policy_request, mocker, caplog):
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture

    # Mock earlier stages to avoid AI service calls
    mock_data_output = StageOutput(content="Mock data", word_count=2, completeness_score=0.9, generation_time=1.0, metadata={})
    mock_risk_output = StageOutput(content="Mock risks", word_count=2, completeness_score=0.8, generation_time=1.0, metadata={})
    mocker.patch.object(generator, '_collect_data', return_value=mock_data_output)
    mocker.patch.object(generator, '_assess_risks', return_value=mock_risk_output)

    # Mock _generate_framework to return None
    mocker.patch.object(generator, '_generate_framework', return_value=None)

    caplog.set_level(logging.ERROR)

    policy_document = await generator.generate_comprehensive_policy(mock_policy_request)

    assert policy_document is None
    assert any(
        f"Policy generation" in record.message and "Framework generation failed" in record.message
        for record in caplog.records
    ), "Expected error message not found in logs"

    # Since we mocked all stages that would call AI service, it should not be called
    mock_ai_service.generate_content.assert_not_called()


@pytest.mark.asyncio
async def test_gcp_framework_raises_exception(enhanced_policy_generator_fixture, mock_policy_request, mocker, caplog):
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture

    # Mock earlier stages to avoid AI service calls
    mock_data_output = StageOutput(content="Mock data", word_count=2, completeness_score=0.9, generation_time=1.0, metadata={})
    mock_risk_output = StageOutput(content="Mock risks", word_count=2, completeness_score=0.8, generation_time=1.0, metadata={})
    mocker.patch.object(generator, '_collect_data', return_value=mock_data_output)
    mocker.patch.object(generator, '_assess_risks', return_value=mock_risk_output)

    # Mock _generate_framework to raise an exception
    test_exception = Exception("Framework AI Error")
    mocker.patch.object(generator, '_generate_framework', side_effect=test_exception)

    caplog.set_level(logging.ERROR)

    policy_document = await generator.generate_comprehensive_policy(mock_policy_request)

    assert policy_document is None
    assert any(
        "Critical error during comprehensive policy generation" in record.message and
        str(test_exception) in record.message
        for record in caplog.records
    ), "Expected error message not found in logs"

    # Since we mocked all stages that would call AI service, it should not be called
    mock_ai_service.generate_content.assert_not_called()


@pytest.mark.asyncio
async def test_gcp_procedures_fails_returns_none(enhanced_policy_generator_fixture, mock_policy_request, mocker, caplog):
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture

    # Mock data collection and risk assessment stages to avoid AI calls
    mock_data_output = StageOutput(content="Mock data", metadata={'generation_id': 'data_id'}, word_count=2, completeness_score=1, generation_time=0.1)
    mock_risk_output = StageOutput(content="Mock risks", metadata={'generation_id': 'risk_id'}, word_count=2, completeness_score=1, generation_time=0.1)
    mocker.patch.object(generator, '_collect_data', return_value=mock_data_output)
    mocker.patch.object(generator, '_assess_risks', return_value=mock_risk_output)

    framework_content = "Mocked Framework Content"
    mock_framework_output = StageOutput(content=framework_content, metadata={'generation_id': 'mock_framework_gen_id'}, word_count=3, completeness_score=0.9, generation_time=0.1)

    # Mock _generate_framework to succeed
    mocker.patch.object(generator, '_generate_framework', return_value=mock_framework_output)
    # Mock _generate_procedures to return None
    mocker.patch.object(generator, '_generate_procedures', return_value=None)

    caplog.set_level(logging.ERROR)

    policy_document = await generator.generate_comprehensive_policy(mock_policy_request)

    assert policy_document is None
    assert any(
        f"Policy generation" in record.message and
        "Procedures generation failed" in record.message
        for record in caplog.records
    ), "Expected error message for procedures failure not found in logs"

    # Since we're mocking all stages directly, no AI calls should be made
    mock_ai_service.generate_content.assert_not_called()


@pytest.mark.asyncio
async def test_gcp_procedures_raises_exception(enhanced_policy_generator_fixture, mock_policy_request, mocker, caplog):
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture

    # Mock data collection and risk assessment stages to avoid AI calls
    mock_data_output = StageOutput(content="Mock data", metadata={'generation_id': 'data_id'}, word_count=2, completeness_score=1, generation_time=0.1)
    mock_risk_output = StageOutput(content="Mock risks", metadata={'generation_id': 'risk_id'}, word_count=2, completeness_score=1, generation_time=0.1)
    mocker.patch.object(generator, '_collect_data', return_value=mock_data_output)
    mocker.patch.object(generator, '_assess_risks', return_value=mock_risk_output)

    framework_content = "Mocked Framework Content"
    mock_framework_output = StageOutput(content=framework_content, metadata={'generation_id': 'mock_framework_gen_id'}, word_count=3, completeness_score=0.9, generation_time=0.1)
    test_exception = Exception("Procedures AI Error")

    # Mock _generate_framework to succeed
    mocker.patch.object(generator, '_generate_framework', return_value=mock_framework_output)
    # Mock _generate_procedures to raise an exception
    mocker.patch.object(generator, '_generate_procedures', side_effect=test_exception)

    caplog.set_level(logging.ERROR)

    policy_document = await generator.generate_comprehensive_policy(mock_policy_request)

    assert policy_document is None
    # The exception test should look for the critical error message format
    assert any(
        "Critical error during comprehensive policy generation" in record.message and
        str(test_exception) in record.message
        for record in caplog.records
    ), "Expected error message for procedures exception not found in logs"

    mock_ai_service.generate_content.assert_not_called()


@pytest.mark.asyncio
async def test_gcp_tools_fails_returns_none(enhanced_policy_generator_fixture, mock_policy_request, mocker, caplog):
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture

    # Mock data collection and risk assessment stages to avoid AI calls
    mock_data_output = StageOutput(content="Mock data", metadata={'generation_id': 'data_id'}, word_count=2, completeness_score=1, generation_time=0.1)
    mock_risk_output = StageOutput(content="Mock risks", metadata={'generation_id': 'risk_id'}, word_count=2, completeness_score=1, generation_time=0.1)
    mocker.patch.object(generator, '_collect_data', return_value=mock_data_output)
    mocker.patch.object(generator, '_assess_risks', return_value=mock_risk_output)

    mock_framework_output = StageOutput(content="Framework", metadata={'generation_id': 'fw_id'}, word_count=1, completeness_score=1, generation_time=0.1)
    mock_procedures_output = StageOutput(content="Procedures", metadata={'generation_id': 'proc_id'}, word_count=1, completeness_score=1, generation_time=0.1)

    mocker.patch.object(generator, '_generate_framework', return_value=mock_framework_output)
    mocker.patch.object(generator, '_generate_procedures', return_value=mock_procedures_output)
    mocker.patch.object(generator, '_generate_tools', return_value=None)

    caplog.set_level(logging.ERROR)

    policy_document = await generator.generate_comprehensive_policy(mock_policy_request)

    assert policy_document is None
    assert any(
        "Policy generation" in record.message and
        "Tools generation failed" in record.message
        for record in caplog.records
    ), "Expected error message for tools failure not found in logs"

    mock_ai_service.generate_content.assert_not_called()


@pytest.mark.asyncio
async def test_gcp_tools_raises_exception(enhanced_policy_generator_fixture, mock_policy_request, mocker, caplog):
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture

    # Mock data collection and risk assessment stages to avoid AI calls
    mock_data_output = StageOutput(content="Mock data", metadata={'generation_id': 'data_id'}, word_count=2, completeness_score=1, generation_time=0.1)
    mock_risk_output = StageOutput(content="Mock risks", metadata={'generation_id': 'risk_id'}, word_count=2, completeness_score=1, generation_time=0.1)
    mocker.patch.object(generator, '_collect_data', return_value=mock_data_output)
    mocker.patch.object(generator, '_assess_risks', return_value=mock_risk_output)

    mock_framework_output = StageOutput(content="Framework", metadata={'generation_id': 'fw_id'}, word_count=1, completeness_score=1, generation_time=0.1)
    mock_procedures_output = StageOutput(content="Procedures", metadata={'generation_id': 'proc_id'}, word_count=1, completeness_score=1, generation_time=0.1)
    test_exception = Exception("Tools AI Error")

    mocker.patch.object(generator, '_generate_framework', return_value=mock_framework_output)
    mocker.patch.object(generator, '_generate_procedures', return_value=mock_procedures_output)
    mocker.patch.object(generator, '_generate_tools', side_effect=test_exception)

    caplog.set_level(logging.ERROR)

    policy_document = await generator.generate_comprehensive_policy(mock_policy_request)

    assert policy_document is None
    # The exception test should look for the critical error message format
    assert any(
        "Critical error during comprehensive policy generation" in record.message and
        str(test_exception) in record.message
        for record in caplog.records
    ), "Expected error message for tools exception not found in logs"

    mock_ai_service.generate_content.assert_not_called()


@pytest.mark.asyncio
async def test_gcp_assembly_fails_returns_none(enhanced_policy_generator_fixture, mock_policy_request, mocker, caplog):
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture

    # Mock data collection and risk assessment stages to avoid AI calls
    mock_data_output = StageOutput(content="Mock data", metadata={'generation_id': 'data_id'}, word_count=2, completeness_score=1, generation_time=0.1)
    mock_risk_output = StageOutput(content="Mock risks", metadata={'generation_id': 'risk_id'}, word_count=2, completeness_score=1, generation_time=0.1)
    mocker.patch.object(generator, '_collect_data', return_value=mock_data_output)
    mocker.patch.object(generator, '_assess_risks', return_value=mock_risk_output)

    mock_framework_output = StageOutput(content="Framework", metadata={'generation_id': 'fw_id'}, word_count=1, completeness_score=1, generation_time=0.1)
    mock_procedures_output = StageOutput(content="Procedures", metadata={'generation_id': 'proc_id'}, word_count=1, completeness_score=1, generation_time=0.1)
    mock_tools_output = StageOutput(content="Tools", metadata={'generation_id': 'tools_id'}, word_count=1, completeness_score=1, generation_time=0.1)

    mocker.patch.object(generator, '_generate_framework', return_value=mock_framework_output)
    mocker.patch.object(generator, '_generate_procedures', return_value=mock_procedures_output)
    mocker.patch.object(generator, '_generate_tools', return_value=mock_tools_output)
    mocker.patch.object(generator, '_assemble_document', return_value=None)

    caplog.set_level(logging.ERROR)

    policy_document = await generator.generate_comprehensive_policy(mock_policy_request)

    assert policy_document is None
    assert any(
        "Policy generation" in record.message and
        "failed at stage assembly" in record.message and
        "Document assembly failed" in record.message
        for record in caplog.records
    ), "Expected error message for assembly failure not found in logs"

    # AI service should not be called by _assemble_document if it's mocked directly
    mock_ai_service.generate_content.assert_not_called()


@pytest.mark.asyncio
async def test_gcp_assembly_raises_exception(enhanced_policy_generator_fixture, mock_policy_request, mocker, caplog):
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture

    # Mock data collection and risk assessment stages to avoid AI calls
    mock_data_output = StageOutput(content="Mock data", metadata={'generation_id': 'data_id'}, word_count=2, completeness_score=1, generation_time=0.1)
    mock_risk_output = StageOutput(content="Mock risks", metadata={'generation_id': 'risk_id'}, word_count=2, completeness_score=1, generation_time=0.1)
    mocker.patch.object(generator, '_collect_data', return_value=mock_data_output)
    mocker.patch.object(generator, '_assess_risks', return_value=mock_risk_output)

    mock_framework_output = StageOutput(content="Framework", metadata={'generation_id': 'fw_id'}, word_count=1, completeness_score=1, generation_time=0.1)
    mock_procedures_output = StageOutput(content="Procedures", metadata={'generation_id': 'proc_id'}, word_count=1, completeness_score=1, generation_time=0.1)
    mock_tools_output = StageOutput(content="Tools", metadata={'generation_id': 'tools_id'}, word_count=1, completeness_score=1, generation_time=0.1)
    test_exception = Exception("Assembly Error")

    mocker.patch.object(generator, '_generate_framework', return_value=mock_framework_output)
    mocker.patch.object(generator, '_generate_procedures', return_value=mock_procedures_output)
    mocker.patch.object(generator, '_generate_tools', return_value=mock_tools_output)
    mocker.patch.object(generator, '_assemble_document', side_effect=test_exception)

    caplog.set_level(logging.ERROR)

    policy_document = await generator.generate_comprehensive_policy(mock_policy_request)

    assert policy_document is None
    # The exception test should look for the critical error message format
    assert any(
        "Critical error during comprehensive policy generation" in record.message and
        str(test_exception) in record.message
        for record in caplog.records
    ), "Expected error message for assembly exception not found in logs"

    mock_ai_service.generate_content.assert_not_called()



@pytest.mark.asyncio
async def test_ai_service_fails_during_framework_generation(enhanced_policy_generator_fixture, mock_policy_request, mocker, caplog):
    """Test that the system provides resilient behavior with fallback content when AI service fails."""
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture
    ai_error = Exception("AI Service Network Error")
    mock_ai_service.generate_content.side_effect = ai_error

    # Mock uuid.uuid4 to have a predictable generation_id
    fixed_uuid_str = '12345678-1234-5678-1234-************'
    mock_uuid = mocker.patch('uuid.uuid4', return_value=uuid.UUID(fixed_uuid_str))

    mock_log_stage_complete = mocker.patch.object(generator, '_log_stage_completion', new_callable=AsyncMock)
    caplog.set_level(logging.ERROR)

    # Act
    policy_document = await generator.generate_comprehensive_policy(mock_policy_request)

    # Assertions - With error recovery system, we expect a PolicyDocument with fallback content
    assert policy_document is not None
    assert isinstance(policy_document, PolicyDocument)
    assert policy_document.policy_id == fixed_uuid_str

    # Verify that fallback content was used in data collection and risk assessment
    assert 'data_collection' in policy_document.content
    assert 'risk_assessment' in policy_document.content
    assert 'framework' in policy_document.content

    # The content should contain error messages as fallback content
    data_content = policy_document.content['data_collection'].content
    assert "AI Service Network Error" in data_content

    risk_content = policy_document.content['risk_assessment'].content
    assert "AI Service Network Error" in risk_content

    # Framework should have fallback content from error recovery
    framework_content = policy_document.content['framework'].content
    assert len(framework_content) > 0  # Should have fallback content

    # Check that error recovery was triggered (error logs should be present)
    error_logs = [record for record in caplog.records if record.levelno == logging.ERROR]
    assert len(error_logs) > 0, "Expected error logs from error recovery attempts"

    # Verify error recovery attempts were logged
    recovery_logs = [log for log in error_logs if "attempting recovery" in log.message]
    assert len(recovery_logs) > 0, "Expected error recovery attempt logs"


@pytest.mark.asyncio
async def test_ai_service_returns_none_during_procedures_generation(enhanced_policy_generator_fixture, mock_policy_request, mocker, caplog):
    """Test that the system provides resilient behavior with fallback content when AI service returns None."""
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture

    fixed_uuid_str = '*************-5678-1234-************' # New UUID for this test
    mocker.patch('uuid.uuid4', return_value=uuid.UUID(fixed_uuid_str))

    framework_content = "Valid framework content"
    # AI service returns content for data collection (3 calls), risk assessment (3 calls), framework (1 call), then None for procedures
    # Total: 3 + 3 + 1 + 1 = 8 calls, but the 8th call (procedures) returns None
    mock_ai_service.generate_content.side_effect = [
        "data1", "data2", "data3",  # data collection
        "risk1", "risk2", "risk3",  # risk assessment
        framework_content,          # framework
        None                        # procedures (fails here, but error recovery provides fallback)
    ]

    # Mock _log_stage_completion to prevent its actual execution for this error path test
    mocker.patch.object(generator, '_log_stage_completion', new_callable=AsyncMock)
    caplog.set_level(logging.ERROR)

    policy_document = await generator.generate_comprehensive_policy(mock_policy_request)

    # With error recovery system, we expect a PolicyDocument with fallback content
    assert policy_document is not None
    assert isinstance(policy_document, PolicyDocument)
    assert policy_document.policy_id == fixed_uuid_str

    # Verify that the early stages succeeded with provided content
    assert 'data_collection' in policy_document.content
    assert 'risk_assessment' in policy_document.content
    assert 'framework' in policy_document.content
    assert 'procedures' in policy_document.content

    # Check that the data collection content matches what we provided
    data_content = policy_document.content['data_collection'].content
    assert "data1" in data_content and "data2" in data_content and "data3" in data_content

    # Check that the risk assessment content matches what we provided
    risk_content = policy_document.content['risk_assessment'].content
    assert "risk1" in risk_content and "risk2" in risk_content and "risk3" in risk_content

    # Check that the framework content matches what we provided
    framework_content_actual = policy_document.content['framework'].content
    assert framework_content in framework_content_actual

    # Procedures should have fallback content from error recovery (not None)
    procedures_content = policy_document.content['procedures'].content
    assert len(procedures_content) > 0  # Should have fallback content

    # Check that error recovery was triggered for procedures stage
    error_logs = [record for record in caplog.records if record.levelno == logging.ERROR]
    assert len(error_logs) > 0, "Expected error logs from error recovery attempts"

    # Verify error recovery attempts were logged for procedures stage
    procedures_recovery_logs = [log for log in error_logs if "procedures" in log.message and "attempting recovery" in log.message]
    assert len(procedures_recovery_logs) > 0, "Expected error recovery attempt logs for procedures stage"




# --- Negative Scenarios: Validator Failures ---
# TODO: Add tests for error conditions (e.g., AI service fails, validation fails, specific stage fails)
# TODO: Add tests for individual private methods if complex enough and not covered by generate_comprehensive_policy tests
# TODO: Add tests for edge cases in PolicyRequest

@pytest.mark.asyncio
async def test_handle_generation_error_logs_to_db(enhanced_policy_generator_fixture, mock_policy_request, mocker):
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture

    test_generation_id = str(uuid.uuid4())
    test_stage = GenerationStage.PROCEDURES
    test_error = ValueError("Something went wrong during procedures")

    # Mock datetime.now(timezone.utc) to control the timestamp
    mock_now = datetime(2023, 10, 26, 12, 0, 0, tzinfo=timezone.utc)
    # Patch datetime as it's imported in the module under test
    mocked_datetime_module = mocker.patch('backend.services.enhanced_policy_generator.datetime')
    mocked_datetime_module.now.return_value = mock_now
    mocked_datetime_module.timezone.utc = timezone.utc # Ensure the mock datetime module also has timezone.utc

    # Call the method directly
    await generator._handle_generation_error(test_error, mock_policy_request, test_generation_id, test_stage)

    # Assert that insert_one was called on the generation_errors collection
    mock_db.generation_errors.insert_one.assert_called_once()

    # Verify the content of the logged error data
    call_args = mock_db.generation_errors.insert_one.call_args
    logged_error_data = call_args[0][0] # The first argument of the first call

    assert logged_error_data["generation_id"] == test_generation_id
    assert logged_error_data["user_id"] == mock_policy_request.user_id
    assert logged_error_data["company_name"] == mock_policy_request.company_name
    assert logged_error_data["framework"] == mock_policy_request.framework
    assert logged_error_data["stage"] == test_stage.value
    assert logged_error_data["error_type"] == type(test_error).__name__
    assert logged_error_data["error_message"] == str(test_error)
    assert logged_error_data["timestamp"] == mock_now


@pytest.mark.asyncio
async def test_save_enhanced_policy_logs_to_db(enhanced_policy_generator_fixture, mock_policy_request, mocker):
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture

    test_policy_id = str(uuid.uuid4())
    mock_now = datetime(2023, 10, 27, 10, 0, 0, tzinfo=timezone.utc)

    # Create a sample StageOutput for each content type
    sample_stage_output = StageOutput(
        content="Sample content for stage",
        word_count=500,
        completeness_score=0.95,
        generation_time=10.5,
        metadata={
            "quality_score": 0.88,
            "issues": 0,
            "model_name": "test-model",
            "prompt_tokens": 100,
            "completion_tokens": 200,
            "total_tokens": 300
        }
    )

    sample_policy_doc = PolicyDocument(
        policy_id=test_policy_id,
        content={
            GenerationStage.FRAMEWORK.value: sample_stage_output,
            GenerationStage.PROCEDURES.value: sample_stage_output,
            GenerationStage.TOOLS.value: sample_stage_output,
            GenerationStage.ASSEMBLY.value: sample_stage_output
        },
        metadata={"user_id": mock_policy_request.user_id, "key": "value"},
        quality_metrics={"overall": 0.9},
        generation_config={"param": "test"}
    )

    # Patch datetime.now as it's used in _save_enhanced_policy
    mocked_datetime_module = mocker.patch('backend.services.enhanced_policy_generator.datetime')
    mocked_datetime_module.now.return_value = mock_now
    mocked_datetime_module.timezone.utc = timezone.utc

    await generator._save_enhanced_policy(sample_policy_doc, mock_policy_request)

    mock_db.policies.insert_one.assert_called_once()
    call_args = mock_db.policies.insert_one.call_args
    logged_policy_data = call_args[0][0]

    assert logged_policy_data["id"] == test_policy_id
    assert logged_policy_data["user_id"] == mock_policy_request.user_id
    assert logged_policy_data["title"] == f"{mock_policy_request.framework} Policy for {mock_policy_request.company_name}"
    assert logged_policy_data["framework"] == mock_policy_request.framework
    assert logged_policy_data["company_name"] == mock_policy_request.company_name
    assert logged_policy_data["industry"] == mock_policy_request.industry
    assert logged_policy_data["content_sections"]["framework"]["content"] == sample_stage_output.content
    assert logged_policy_data["metadata"] == sample_policy_doc.metadata
    assert logged_policy_data["quality_metrics"] == sample_policy_doc.quality_metrics
    assert logged_policy_data["generation_config"] == sample_policy_doc.generation_config
    assert logged_policy_data["version"] == 1
    assert logged_policy_data["status"] == "draft"
    assert logged_policy_data["created_at"] == mock_now
    assert logged_policy_data["updated_at"] == mock_now


@pytest.mark.asyncio
async def test_assemble_document_ai_driven(enhanced_policy_generator_fixture, mock_policy_request):
    """Test the AI-driven document assembly stage."""
    generator, mock_ai_service, mock_db, mock_pm, mock_qv = enhanced_policy_generator_fixture

    # Arrange
    framework_output = StageOutput(content="Framework Content", word_count=100, completeness_score=0.9, generation_time=1.0, metadata={})
    procedures_output = StageOutput(content="Procedures Content", word_count=200, completeness_score=0.85, generation_time=1.0, metadata={})
    tools_output = StageOutput(content="Tools Content", word_count=150, completeness_score=0.88, generation_time=1.0, metadata={})
    data_context = StageOutput(content="Data Context", word_count=50, completeness_score=0.9, generation_time=1.0, metadata={})
    risk_context = StageOutput(content="Risk Context", word_count=75, completeness_score=0.8, generation_time=1.0, metadata={})
    
    assembled_content = "Final Assembled Document from AI"
    mock_ai_service.generate_content.return_value = assembled_content

    # Act
    result = await generator._assemble_document(
        framework_output, procedures_output, tools_output, mock_policy_request, "test_gen_id",
        data_context=data_context, risk_context=risk_context
    )

    # Assert
    assert result is not None
    assert result.content == assembled_content
    assert result.word_count == 5 # "Final Assembled Document from AI"

    # Verify that the correct prompt was generated and passed to the AI service
    mock_pm.get_assembly_prompt.assert_called_once_with(
        framework_output, procedures_output, tools_output, mock_policy_request,
        data_context=data_context, risk_context=risk_context
    )
    
    # Verify that the AI service was called with the assembly prompt
    mock_ai_service.generate_content.assert_called_once_with("Assembly Prompt", max_tokens=8192)

    # Verify that the quality validator was called
    mock_qv.validate_assembly.assert_called_once_with(assembled_content)
